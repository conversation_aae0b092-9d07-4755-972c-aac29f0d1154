using BlueTape.PaymentService.Application.Abstractions.Services;
using BlueTape.PaymentService.DataAccess.Abstractions.Repositories;
using BlueTape.PaymentService.Domain.Entities;
using BlueTape.PaymentService.Domain.Enums;
using BlueTape.PaymentService.Domain.Models;
using BlueTape.Utilities.Providers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace BlueTape.PaymentService.Application.Services;

public class PaymentWindowService(
    IPaymentConfigRepository paymentConfigRepository,
    IConfiguration configuration,
    IDateProvider dateProvider,
    ILogger<PaymentWindowService> logger)
    : IPaymentWindowService
{
    private const string PaymentWindowConfigKey = "PAYMENT_WINDOW_CONFIG";

    public async Task<bool> IsPaymentWindowActive(CancellationToken ct)
    {
        var config = await GetPaymentWindowConfig(ct);

        if (!config.IsPaymentWindowEnabled)
            return false;

        var currentTime = dateProvider.CurrentDateTime;

        return IsAnyPaymentWindowActive(config, currentTime);
    }

    public List<PaymentWindow> GetActivePaymentWindows(PaymentWindowConfig config)
    {
        if (!config.IsPaymentWindowEnabled)
            return new List<PaymentWindow>();

        var currentTime = dateProvider.CurrentDateTime;
        return GetActivePaymentWindows(config, currentTime);
    }

    private bool IsAnyPaymentWindowActive(PaymentWindowConfig config, DateTime currentTime)
    {
        return GetActivePaymentWindows(config, currentTime).Any();
    }

    private List<PaymentWindow> GetActivePaymentWindows(PaymentWindowConfig config, DateTime currentTime)
    {
        var activeWindows = new List<PaymentWindow>();
        var currentDay = currentTime.DayOfWeek;

        foreach (var window in config.PaymentWindows.Where(w => w.IsEnabled))
        {
            if (!window.ActiveDays.Contains(currentDay))
                continue;

            var windowStart = currentTime.Date.Add(window.StartTime);
            var windowEnd = windowStart.AddMinutes(window.DurationMinutes);

            if (currentTime >= windowStart && currentTime <= windowEnd)
            {
                activeWindows.Add(window);

                logger.LogDebug(
                    "Payment window '{WindowName}' is active: Current={CurrentTime}, WindowStart={WindowStart}, WindowEnd={WindowEnd}",
                    window.Name, currentTime, windowStart, windowEnd);
            }
        }

        return activeWindows;
    }

    public async Task<PaymentWindowConfig> GetPaymentWindowConfig(CancellationToken ct)
    {
        return LoadConfigFromDatabaseOrDefault(ct).GetAwaiter().GetResult();
    }

    private async Task<PaymentWindowConfig> LoadConfigFromDatabaseOrDefault(CancellationToken ct)
    {
        var configEntity = await paymentConfigRepository.GetByConfigKey(PaymentWindowConfigKey, ct);

        if (configEntity == null)
        {
            logger.LogInformation("Payment window config not found in database, loading from appsettings and initializing database");

            // Load from appsettings
            var defaultConfig = LoadConfigFromAppSettings();

            // Save to database for future use
            await SaveConfigToDatabase(defaultConfig, "System", ct);

            return defaultConfig;
        }

        try
        {
            var config = JsonSerializer.Deserialize<PaymentWindowConfig>(configEntity.ConfigValue);
            logger.LogDebug("Payment window configuration loaded from database");
            return config ?? LoadConfigFromAppSettings();
        }
        catch (JsonException ex)
        {
            logger.LogError(ex, "Failed to deserialize payment window configuration from database, falling back to appsettings");
            return LoadConfigFromAppSettings();
        }
    }

    private PaymentWindowConfig LoadConfigFromAppSettings()
    {
        try
        {
            var configSection = configuration.GetSection("PaymentWindowConfig");
            var config = configSection.Get<PaymentWindowConfig>();

            if (config == null)
            {
                logger.LogWarning("PaymentWindowConfig section not found in appsettings, using hardcoded defaults");
                return new PaymentWindowConfig();
            }

            logger.LogDebug("Payment window configuration loaded from appsettings");
            return config;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to load payment window configuration from appsettings, using hardcoded defaults");
            return new PaymentWindowConfig();
        }
    }

    private async Task SaveConfigToDatabase(PaymentWindowConfig config, string createdBy, CancellationToken ct)
    {
        try
        {
            config.UpdatedAt = dateProvider.CurrentDateTime;
            config.UpdatedBy = createdBy;

            var configJson = JsonSerializer.Serialize(config);
            var configEntity = new PaymentConfigEntity
            {
                ConfigKey = PaymentWindowConfigKey,
                ConfigValue = configJson,
                Description = "Payment window configuration for ACH/Same Day ACH processing",
                CreatedAt = dateProvider.CurrentDateTime,
                CreatedBy = createdBy,
                UpdatedAt = dateProvider.CurrentDateTime,
                UpdatedBy = createdBy
            };

            await paymentConfigRepository.Add(configEntity, ct);
            logger.LogInformation("Payment window configuration saved to database by {CreatedBy}", createdBy);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to save payment window configuration to database");
        }
    }

    public async Task UpdatePaymentWindowConfig(PaymentWindowConfig config, string updatedBy, CancellationToken ct)
    {
        config.UpdatedAt = dateProvider.CurrentDateTime;
        config.UpdatedBy = updatedBy;

        var configJson = JsonSerializer.Serialize(config);
        var configEntity = await paymentConfigRepository.GetByConfigKey(PaymentWindowConfigKey, ct);

        if (configEntity == null)
        {
            configEntity = new PaymentConfigEntity
            {
                ConfigKey = PaymentWindowConfigKey,
                ConfigValue = configJson,
                Description = "Payment window configuration for ACH/Same Day ACH processing",
                CreatedAt = dateProvider.CurrentDateTime,
                CreatedBy = updatedBy
            };
            await paymentConfigRepository.Add(configEntity, ct);
        }
        else
        {
            configEntity.ConfigValue = configJson;
            configEntity.UpdatedAt = dateProvider.CurrentDateTime;
            configEntity.UpdatedBy = updatedBy;
            await paymentConfigRepository.Update(configEntity, ct);
        }

        logger.LogInformation("Payment window configuration updated by {UpdatedBy}", updatedBy);
    }

    public bool ShouldProcessPaymentMethod(PaymentWindowConfig config, PaymentRequestCommandEntity command, CancellationToken ct)
    {
        var paymentType = command.PaymentRequest.RequestType;
        var paymentMethod = command.PaymentRequest.PaymentMethod;

        // If payment not affected by payment windows or is a prefunded payment in processing status
        if (!config.AffectedPaymentTypes.Contains(paymentType) || ((IsPrefundedFinalPayment(command) && command.PaymentRequest.Status == PaymentRequestStatus.Processing)))
        {
            return true;
        }

        var activeWindows = GetActivePaymentWindows(config);

        if (!config.IsPaymentWindowEnabled)
        {
            return config.DefaultConfig.AllowedPaymentMethods.Contains(paymentMethod);
        }

        // If any windows active - filter by method and type
        if (activeWindows.Any())
        {
            return activeWindows.Any(window => window.AllowedPaymentMethods.Contains(paymentMethod) && window.AllowedPaymentTypes.Contains(paymentType));
        }

        return config.DefaultConfig.AllowedPaymentMethods.Contains(paymentMethod);
    }

    public bool IsPrefundedFinalPayment(PaymentRequestCommandEntity command)
    {
        if (command.PaymentRequest == null)
            return false;

        return command.PaymentRequest.RequestType == PaymentRequestType.FinalPaymentV2 ||
               command.PaymentRequest.RequestType == PaymentRequestType.FactoringFinalPayment;
    }

    public List<PaymentRequestCommandEntity> PrioritizeCommands(List<PaymentRequestCommandEntity> commands, CancellationToken ct)
    {
        var paymentMethodPriority = new Dictionary<PaymentMethod, int>
        {
            { PaymentMethod.Instant, 3 },
            { PaymentMethod.Wire, 3 },
            { PaymentMethod.SameDayAch, 4 },
            { PaymentMethod.Ach, 5 },
        };

        // This implementation should process already processing payments as well
        var prioritizedCommands = commands.OrderBy(x => !IsPrefundedFinalPayment(x))
            .ThenBy(x => paymentMethodPriority.TryGetValue(x.PaymentRequest.PaymentMethod, out var order) ? order : int.MaxValue)
            .ThenBy(x => x.PaymentRequest.ConfirmedAt)
            .ThenBy(x => x.PaymentRequest.CreatedAt)
            .ToList();

        logger.LogDebug("Commands prioritized: Final payments: {FinalCount}, Total: {TotalCount}",
            prioritizedCommands.Count(IsPrefundedFinalPayment), prioritizedCommands.Count);

        return prioritizedCommands;
    }

    public async Task<List<PaymentRequestCommandEntity>> FilterCommandsByPaymentWindow(List<PaymentRequestCommandEntity> commands, CancellationToken ct)
    {
        var config = await GetPaymentWindowConfig(ct);
        var activeWindows = GetActivePaymentWindows(config);

        if (!config.IsPaymentWindowEnabled)
        {
            // If payment windows are disabled - return all commands
            logger.LogInformation("Payment windows are disabled or no active windows, returning all commands");
            return commands;
        }

        var filteredCommands = new List<PaymentRequestCommandEntity>();

        foreach (var command in commands)
        {
            if (ShouldProcessPaymentMethod(config, command, ct))
            {
                filteredCommands.Add(command);
            }
        }

        // Filtered out payment requests that has been approved after the start of the active payment window but allow to process prefunded reqeusts
        if (activeWindows.Any())
        {
            var windowStartDate = dateProvider.CurrentDate.ToDateTime(TimeOnly.MinValue) + activeWindows.FirstOrDefault().StartTime;
            filteredCommands =
                filteredCommands.Where(x => !config.AffectedPaymentTypes.Contains(x.PaymentRequest.RequestType)
                                            || (IsPrefundedFinalPayment(x) && x.PaymentRequest.Status == PaymentRequestStatus.Processing)
                                            || x.PaymentRequest.ConfirmedAt < windowStartDate).ToList();
        }

        logger.LogInformation(
            "Filtered commands by payment window: {FilteredCount} out of {TotalCount} commands will be processed",
            filteredCommands.Count, commands.Count);

        return filteredCommands;
    }
}
